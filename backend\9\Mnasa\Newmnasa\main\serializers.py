from django.contrib.auth import get_user_model
from rest_framework import serializers
from .models import User, InstructorProfile, Course, Order, Review, DigitalProduct, Payment, Lesson, Quiz, Question, Answer, Certificate, Announcement, FAQ, Category, ReviewComment, InstructorAvailability, Notification, UserQuizAttempt

User = get_user_model()

# -------------------- UserSerializer --------------------
class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    payment_details = serializers.JSONField(required=False, allow_null=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'password',
            'phone_number', 'profile_image', 'bio',
            'date_of_birth', 'language', 'is_instructor', 'is_student',
            'wallet_number', 'payment_method', 'payment_details', 'first_name', 'last_name'
        ]
        read_only_fields = ['id']
        extra_kwargs = {
            'password': {'write_only': True},
            'phone_number': {'required': False},
            'profile_image': {'required': False},
            'wallet_number': {'required': False},
            'payment_method': {'required': False},
            'payment_details': {'required': False},
        }

    def create(self, validated_data):
        password = validated_data.pop('password')
        user = User(**validated_data)
        user.set_password(password)
        user.save()
        return user

    def validate(self, data):
        if data.get('is_instructor'):
            if data.get('payment_method') and not data.get('wallet_number'):
                raise serializers.ValidationError("يجب إدخال رقم المحفظة مع وسيلة الدفع.")
            if data.get('wallet_number') and not data.get('payment_method'):
                raise serializers.ValidationError("يجب اختيار وسيلة الدفع مع رقم المحفظة.")
        return data

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if not instance.is_instructor:
            ret.pop('wallet_number', None)
            ret.pop('payment_method', None)
        else:
            if instance.wallet_number and not self.context['request'].user.is_staff:
                ret['wallet_number'] = instance.wallet_number
                self.fields['wallet_number'].read_only = True
            if instance.payment_method and not self.context['request'].user.is_staff:
                ret['payment_method'] = instance.payment_method
                self.fields['payment_method'].read_only = True
        return ret

# -------------------- ReviewSerializer --------------------
class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    class Meta:
        model = Review
        fields = ['id', 'course', 'user', 'rating', 'comment', 'created_at', 'is_approved', 'reply']
        read_only_fields = ['id', 'user', 'created_at']

# -------------------- CourseReadSerializer --------------------
class CourseReadSerializer(serializers.ModelSerializer):
    instructor = UserSerializer(read_only=True)
    students_count = serializers.SerializerMethodField()
    rating = serializers.SerializerMethodField()
    reviews = ReviewSerializer(many=True, read_only=True)
    students = UserSerializer(many=True, read_only=True)
    likes_count = serializers.SerializerMethodField()
    is_liked = serializers.SerializerMethodField()

    class Meta:
        model = Course
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']
        extra_kwargs = {
            'promo_video': {'required': False, 'allow_null': True}
        }

    def get_students_count(self, obj):
        return obj.students.count()

    def get_rating(self, obj):
        reviews = obj.reviews.filter(is_approved=True)
        if reviews.exists():
            return round(sum([r.rating for r in reviews]) / reviews.count(), 2)
        return 0

    def get_likes_count(self, obj):
        return obj.likes.count()

    def get_is_liked(self, obj):
        user = self.context.get('request').user if self.context.get('request') else None
        if user and user.is_authenticated:
            return obj.likes.filter(id=user.id).exists()
        return False

# -------------------- CourseWriteSerializer --------------------
class CourseWriteSerializer(serializers.ModelSerializer):
    def validate_price(self, value):
        if value < 0:
            raise serializers.ValidationError("سعر الكورس لا يمكن أن يكون أقل من صفر.")
        return value

    class Meta:
        model = Course
        exclude = ['id', 'created_at', 'updated_at', 'instructor', 'students', 'likes']
        extra_kwargs = {
            'promo_video': {'required': False, 'allow_null': True}
        }

# -------------------- InstructorWithCoursesSerializer --------------------
class InstructorWithCoursesSerializer(serializers.ModelSerializer):
    courses = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'is_instructor', 'is_student', 'profile_image', 'bio',
            'date_of_birth', 'language', 'courses'
        ]

    def get_courses(self, obj):
        courses = Course.objects.filter(instructor=obj)
        return CourseReadSerializer(courses, many=True, context=self.context).data

class DigitalProductSerializer(serializers.ModelSerializer):
    seller = UserSerializer(read_only=True)
    
    class Meta:
        model = DigitalProduct
        fields = ['id', 'title', 'description', 'price', 'seller', 'file', 'thumbnail', 'created_at', 'updated_at', 'is_published']
        read_only_fields = ['id', 'created_at', 'updated_at']

class OrderSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    course = CourseReadSerializer(read_only=True)
    product = DigitalProductSerializer(read_only=True)
    
    class Meta:
        model = Order
        fields = ['id', 'user', 'course', 'product', 'amount', 'status', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class PaymentSerializer(serializers.ModelSerializer):
    order = OrderSerializer(read_only=True)
    
    class Meta:
        model = Payment
        fields = ['id', 'order', 'amount', 'payment_method', 'transaction_id', 'status', 'created_at']
        read_only_fields = ['id', 'created_at']

class LessonSerializer(serializers.ModelSerializer):
    video_info = serializers.SerializerMethodField()
    resources = serializers.SerializerMethodField()
    quizzes = serializers.SerializerMethodField()  # ✅ إضافة هذا السطر لعرض الامتحانات المرتبطة بالدرس

    def get_resources(self, obj):
        print("🔥 get_resources called for:", obj.title)
        if obj.resources:
            return obj.resources.url
        return None

    def get_quizzes(self, obj):
        # جلب الامتحانات المرتبطة بالدرس
        quizzes = obj.quizzes.all()
        return QuizSerializer(quizzes, many=True, read_only=True).data

    class Meta:
        model = Lesson
        fields = [
            'id', 'course', 'title', 'content', 'lesson_type',
            'is_preview', 'video', 'video_info', 'duration',
            'order', 'resources', 'quizzes'  # ✅ إضافة quizzes هنا
        ]
        read_only_fields = ['video_info']

    def get_video_info(self, obj):
        if not obj.video_public_id:
            return None
        return {
            'duration': obj.video_duration,
            'public_id': obj.video_public_id,
            'is_uploaded': bool(obj.video)
        }
    
    def get_video_url(self, obj):
        if obj.video:
            return obj.video.url
        return None

class QuizSerializer(serializers.ModelSerializer):
    questions = serializers.SerializerMethodField()

    class Meta:
        model = Quiz
        fields = ['id', 'lesson', 'title', 'description', 'passing_score', 'time_limit', 'quiz_type', 'questions']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_questions(self, obj):
        questions = obj.questions.all().order_by('order')
        return QuestionSerializer(questions, many=True, context=self.context).data

class AnswerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Answer
        fields = ['id', 'question', 'text', 'is_correct']
        read_only_fields = ['id', 'created_at', 'updated_at']

# تحديث QuestionSerializer لدعم الصور - زكي الخولي
class QuestionSerializer(serializers.ModelSerializer):
    answers = serializers.SerializerMethodField()
    image_url = serializers.SerializerMethodField()  # إضافة حقل لرابط الصورة - زكي الخولي

    class Meta:
        model = Question
        fields = ['id', 'quiz', 'text', 'question_type', 'points', 'order', 'answers', 'image', 'image_url']
        read_only_fields = ['id', 'created_at', 'updated_at', 'image_url']

    def get_answers(self, obj):
        answers = obj.answers.all()
        return AnswerSerializer(answers, many=True).data

    def get_image_url(self, obj):
        # إرجاع رابط الصورة إذا كانت موجودة - زكي الخولي
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None

class CertificateSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    course = CourseReadSerializer(read_only=True)
    
    class Meta:
        model = Certificate
        fields = ['id', 'user', 'course', 'issued_at', 'certificate_url', 'verification_code']
        read_only_fields = ['id', 'created_at', 'updated_at']

class AnnouncementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Announcement
        fields = ['id', 'course', 'title', 'content', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class FAQSerializer(serializers.ModelSerializer):
    class Meta:
        model = FAQ
        fields = ['id', 'course', 'question', 'answer', 'order']
        read_only_fields = ['id', 'created_at', 'updated_at']

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'slug', 'description', 'parent']


class InstructorProfileSerializer(serializers.ModelSerializer):
    payment_details = serializers.JSONField(required=False, allow_null=True)
    class Meta:
        model = InstructorProfile
        fields = ['id', 'user', 'specialization', 'qualifications', 'website', 'linkedin', 'payment_method', 'payment_details', 'is_approved']
        read_only_fields = ['user', 'is_approved']

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        # أضف payment_details من user إذا لم يكن موجوداً في البروفايل
        if not ret.get('payment_details') and hasattr(instance, 'user'):
            user_payment_details = getattr(instance.user, 'payment_details', None)
            if user_payment_details:
                ret['payment_details'] = user_payment_details
        return ret

class ReviewCommentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    replies = serializers.SerializerMethodField()

    class Meta:
        model = ReviewComment
        fields = ['id', 'review', 'user', 'text', 'created_at', 'parent', 'replies']
        read_only_fields = ['id', 'user', 'created_at', 'replies']

    def get_replies(self, obj):
        # جلب الردود على هذا التعليق
        replies = obj.replies.all().order_by('created_at')
        return ReviewCommentSerializer(replies, many=True, context=self.context).data

class InstructorAvailabilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = InstructorAvailability
        fields = ['id', 'user', 'day', 'from_time', 'to_time', 'timezone', 'enabled', 'note', 'created_at', 'updated_at']
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'message', 'link', 'created_at', 'is_read']

class UserQuizAttemptSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    quiz = QuizSerializer(read_only=True)

    class Meta:
        model = UserQuizAttempt
        fields = [
            'id', 'user', 'quiz', 'score', 'passed',
            'answers', 'submitted', 'submitted_at', 'created_at', 'completed_at'
        ]
        read_only_fields = fields