"use client";
import React, { useEffect, useState, useRef } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import Cookies from "js-cookie";
import Hls from "hls.js";
import axios from "axios";
import {
  fetchInstructorCourse,
  fetchInstructorLessons,
  deleteInstructorLessons,
  fetchCourseVideo,
  approveReview,
  deleteReview,
  replyToReview,
  fetchReviewComments,
  addReviewComment,
} from "../../../../../services/instructor";

export default function CourseDetails() {
  // ================== المتغيرات الرئيسية ==================
  const params = useParams();
  const router = useRouter();
  const courseId = params.id;
  const [courseData, setCourseData] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [videoUrl, setVideoUrl] = useState(null);
  const [videoToken, setVideoToken] = useState(null);
  const [isClient, setIsClient] = useState(false);
  const [approving, setApproving] = useState(false);
  const [reviewComments, setReviewComments] = useState({});
  const [commentText, setCommentText] = useState({});
  const [commentLoading, setCommentLoading] = useState({});
  const [replyText, setReplyText] = useState({});
  const [replying, setReplying] = useState({});
  const [publishing, setPublishing] = useState(false);
  const [expandedLesson, setExpandedLesson] = useState(null);
  const [showQuizForm, setShowQuizForm] = useState({});
  const [quizForm, setQuizForm] = useState({});
  const [showFileForm, setShowFileForm] = useState({});
  const [fileForm, setFileForm] = useState({});
  const fileInputRefs = useRef({});
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadSuccess, setUploadSuccess] = useState({});
  const [uploadError, setUploadError] = useState({});
  const [uploadedFile, setUploadedFile] = useState({});
  const [isUploading, setIsUploading] = useState({});
  const [deletingResource, setDeletingResource] = useState({});
  const [quizEditForm, setQuizEditForm] = useState(null);
  const [questionForm, setQuestionForm] = useState({}); // لإضافة سؤال جديد
  const [showQuestionForm, setShowQuestionForm] = useState({}); // إظهار فورم إضافة سؤال
  const [questionEditForm, setQuestionEditForm] = useState(null); // لتعديل سؤال
  const [questionLoading, setQuestionLoading] = useState({}); // حالة تحميل الأسئلة
  // ================== إضافة state لإجابات السؤال ==================
  const [answersForm, setAnswersForm] = useState({}); // { [quizId]: [ { text, is_correct }, ... ] }
  // ================== إضافة state لإدارة الصور - زكي الخولي ==================
  const [questionImages, setQuestionImages] = useState({}); // { [quizId]: File }
  const [imagePreview, setImagePreview] = useState({}); // { [quizId]: string }
  const [showImageCropper, setShowImageCropper] = useState({}); // { [quizId]: boolean }
  const [croppedImages, setCroppedImages] = useState({}); // { [quizId]: Blob }

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);
      const token = Cookies.get("authToken");
      if (!token) {
        setError("يرجى تسجيل الدخول أولاً");
        setLoading(false);
        return;
      }
      try {
        const course = await fetchInstructorCourse(courseId, token);
        setCourseData(course);
        const lessonsList = await fetchInstructorLessons(courseId, token);
        setLessons(lessonsList);
        console.log(lessonsList)

      } catch (err) {
        setError("فشل في جلب بيانات الكورس أو الدروس");
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [courseId]);

  // التنقل لصفحة الدرس
  const handleSeeLesson = (lessonId) => {
    router.push(`/instructor/dashboard/${courseId}/lesson/${lessonId}`);
  };

  // الموافقة أو الرفض على التقييم
  const handleApproveReview = async (reviewId, approve = true) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setApproving(true);
    try {
      await approveReview(reviewId, approve, token);
      const course = await fetchInstructorCourse(courseId, token);
      setCourseData(course);
    } catch {
      setError("فشل في تحديث حالة التقييم");
    } finally {
      setApproving(false);
    }
  };

  // حذف التقييم
  const handleDeleteReview = async (reviewId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setApproving(true);
    try {
      await deleteReview(reviewId, token);
      const course = await fetchInstructorCourse(courseId, token);
      setCourseData(course);
    } catch {
      setError("فشل في حذف التقييم");
    } finally {
      setApproving(false);
    }
  };

  // الرد على التقييم
  const handleReply = async (reviewId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setReplying((prev) => ({ ...prev, [reviewId]: true }));
    try {
      await replyToReview(reviewId, replyText[reviewId], token);
      setReplyText((prev) => ({ ...prev, [reviewId]: "" }));
      const course = await fetchInstructorCourse(courseId, token);
      setCourseData(course);
    } catch {
      setError("فشل في إرسال الرد");
    } finally {
      setReplying((prev) => ({ ...prev, [reviewId]: false }));
    }
  };

  // تحميل تعليقات التقييمات
  useEffect(() => {
    if (!courseData || !courseData.reviews) return;
    const token = Cookies.get("authToken");
    courseData.reviews.forEach(async (review) => {
      try {
        const comments = await fetchReviewComments(review.id, token);
        setReviewComments((prev) => ({ ...prev, [review.id]: comments }));
      } catch {
        setReviewComments((prev) => ({ ...prev, [review.id]: [] }));
      }
    });
  }, [courseData]);

  // إضافة تعليق
  const handleAddComment = async (reviewId, parentId = null) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setCommentLoading((prev) => ({ ...prev, [reviewId]: true }));
    try {
      await addReviewComment(reviewId, commentText[reviewId], parentId, token);
      setCommentText((prev) => ({ ...prev, [reviewId]: "" }));
      const comments = await fetchReviewComments(reviewId, token);
      setReviewComments((prev) => ({ ...prev, [reviewId]: comments }));
    } catch {
      setError("فشل في إضافة التعليق");
    } finally {
      setCommentLoading((prev) => ({ ...prev, [reviewId]: false }));
    }
  };

  // حذف الدرس
  async function handleDeleteLesson(lessonId) {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    if (!confirm("هل أنت متأكد من حذف هذا الدرس؟")) {
      return;
    }
    // Hosasm deleteLessonById(courseId, lessonId, token);
    try {
      await deleteInstructorLessons(courseId, lessonId, token);
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
      console.log(lessonsList)

    } catch {
      setError("فشل في حذف الدرس");
    }
  }

  // نشر/إلغاء نشر الكورس
  const togglePublish = async () => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setPublishing(true);
    try {
      const formData = new FormData();
      formData.append("is_published", !courseData.is_published);
      await axios.patch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/courses/${courseId}/`,
        formData,
        {
          headers:
          {
            Authorization: `Bearer ${token}`,
            // لا تضع Content-Type هنا، axios يضبطه تلقائيًا مع formData
          },
        }
      );
      // إعادة تحميل بيانات الكورس
      const course = await fetchInstructorCourse(courseId, token);
      setCourseData(course);
    } catch {
      setError("فشل في تحديث حالة النشر");
    } finally {
      setPublishing(false);
    }
  };

  // تهيئة مشغل الفيديو
  useEffect(() => {
    if (videoUrl && videoToken && isClient) {
      const video = document.getElementById("course-video");
      if (!video) return;
      if (Hls.isSupported()) {
        const hls = new Hls({
          xhrSetup: function (xhr) {
            xhr.setRequestHeader("Authorization", `Bearer ${videoToken}`);
          },
        });
        hls.loadSource(videoUrl);
        hls.attachMedia(video);
        hls.on(Hls.Events.MANIFEST_PARSED, function () {
          video.play();
        });
        hls.on(Hls.Events.ERROR, function (event, data) {
          if (data.fatal) {
            setError("حدث خطأ أثناء تشغيل الفيديو");
          }
        });
      } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
        video.src = videoUrl;
        video.addEventListener("loadedmetadata", function () {
          video.play();
        });
      } else {
        setError("متصفحك لا يدعم تشغيل هذا الفيديو");
      }
    }
  }, [videoUrl, videoToken, isClient]);

  // إضافة اختبار أو واجب
  const handleAddQuiz = async (lessonId, quizType) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/`,
        {
          lesson: lessonId,
          title: quizForm[lessonId]?.title || "",
          description: quizForm[lessonId]?.description || "",
          passing_score: quizForm[lessonId]?.passing_score || 70,
          time_limit: quizForm[lessonId]?.time_limit || 0,
          quiz_type: quizType,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      setShowQuizForm((prev) => ({ ...prev, [lessonId]: false }));
      setQuizForm((prev) => ({ ...prev, [lessonId]: {} }));
      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
      console.log(lessonsList)

    } catch {
      setError("فشل في إضافة الاختبار/الواجب");
    }
  };

  // رفع ملف resource
  const handleAddFile = async (lessonId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }

    const file = fileInputRefs.current[lessonId]?.files[0];
    console.log("📄 الملف المحدد:", file);
    if (!file) {
      setError("يرجى اختيار ملف");
      return;
    }

    const formData = new FormData();
    formData.append("resources", file);

    // ⬇️ تهيئة الحالات
    setIsUploading(prev => ({ ...prev, [lessonId]: true }));
    setUploadProgress(prev => ({ ...prev, [lessonId]: 0 }));
    setUploadSuccess(prev => ({ ...prev, [lessonId]: false }));
    setUploadError(prev => ({ ...prev, [lessonId]: null }));

    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/lessons/${lessonId}/upload-resource/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          onUploadProgress: (progressEvent) => {
            const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log("📦 progress:", percent);
            setUploadProgress(prev => ({ ...prev, [lessonId]: percent }));
          },
        }
      );

      // ✅ حفظ رابط الملف
      const fileUrl = response.data?.resources;
      if (fileUrl) {
        setUploadedFile(prev => ({ ...prev, [lessonId]: fileUrl }));
      }

      setUploadSuccess(prev => ({ ...prev, [lessonId]: true }));

      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
      console.log(lessonsList);
    } catch (error) {
      console.error("❌ رفع الملف فشل:", error);
      setUploadError(prev => ({ ...prev, [lessonId]: "فشل في رفع الملف" }));
    } finally {
      setIsUploading(prev => ({ ...prev, [lessonId]: false }));
      setTimeout(() => {
        setUploadSuccess(prev => ({ ...prev, [lessonId]: false }));
        setUploadError(prev => ({ ...prev, [lessonId]: null }));
        setUploadProgress(prev => ({ ...prev, [lessonId]: 0 }));
      }, 2000);
    }
  };

  const handleDeleteResource = async (lessonId) => {
     const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setDeletingResource(prev => ({ ...prev, [lessonId]: true }));
    try {
      await axios.delete(`${process.env.NEXT_PUBLIC_API_URL}/api/lessons/${lessonId}/delete_resource/`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      // أخفي الملف فوراً من الواجهة
      setLessons(prevLessons => prevLessons.map(lesson => lesson.id === lessonId ? { ...lesson, resources: null } : lesson));
      setUploadedFile(prev => ({ ...prev, [lessonId]: null }));
      setShowFileForm(prev => ({ ...prev, [lessonId]: true }));
    } catch (error) {
      console.error("❌ حذف الملف فشل:", error);
    } finally {
      setDeletingResource(prev => ({ ...prev, [lessonId]: false }));
    }
  };

  // حذف كويز أو واجب
  const handleDeleteQuiz = async (lessonId, quizId) => {
    if (!window.confirm("هل أنت متأكد من حذف هذا الامتحان/الواجب؟")) return;
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    try {
      await axios.delete(`${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quizId}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
      setQuizEditForm(null);
    } catch {
      setError("فشل في حذف الامتحان/الواجب");
    }
  };

  // تعديل كويز أو واجب
  const handleEditQuiz = async (lessonId, quizId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    try {
      await axios.patch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/quizzes/${quizId}/`,
        {
          title: quizEditForm.quiz.title,
          description: quizEditForm.quiz.description,
          passing_score: quizEditForm.quiz.passing_score,
          time_limit: quizEditForm.quiz.time_limit,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
      setQuizEditForm(null);
    } catch {
      setError("فشل في تعديل الامتحان/الواجب");
    }
  };

  // إضافة سؤال جديد
  const handleAddQuestion = async (quizId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setQuestionLoading((prev) => ({ ...prev, [quizId]: true }));
    try {
      await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/questions/`,
        {
          quiz: quizId,
          text: questionForm[quizId]?.text || "",
          question_type: questionForm[quizId]?.question_type || "text",
          points: questionForm[quizId]?.points || 1,
          order: questionForm[quizId]?.order || 1,
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setShowQuestionForm((prev) => ({ ...prev, [quizId]: false }));
      setQuestionForm((prev) => ({ ...prev, [quizId]: {} }));
      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
    } catch {
      setError("فشل في إضافة السؤال");
    } finally {
      setQuestionLoading((prev) => ({ ...prev, [quizId]: false }));
    }
  };

  // ================== تعديل فورم إضافة سؤال ==================
  // ================== دوال التعامل مع الصور - زكي الخولي ==================
  const handleImageSelect = (quizId, file) => {
    if (file && file.type.startsWith('image/')) {
      setQuestionImages(prev => ({ ...prev, [quizId]: file }));
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(prev => ({ ...prev, [quizId]: e.target.result }));
        setShowImageCropper(prev => ({ ...prev, [quizId]: true }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleImageCrop = (quizId, croppedBlob) => {
    setCroppedImages(prev => ({ ...prev, [quizId]: croppedBlob }));
    setShowImageCropper(prev => ({ ...prev, [quizId]: false }));
  };

  const removeQuestionImage = (quizId) => {
    setQuestionImages(prev => ({ ...prev, [quizId]: null }));
    setImagePreview(prev => ({ ...prev, [quizId]: null }));
    setCroppedImages(prev => ({ ...prev, [quizId]: null }));
    setShowImageCropper(prev => ({ ...prev, [quizId]: false }));
  };

  // ================== منطق إضافة سؤال مع الإجابات ==================
  const handleAddQuestionWithAnswers = async (quizId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setQuestionLoading((prev) => ({ ...prev, [quizId]: true }));
    try {
      // جلب نوع الكويز (امتحان أو واجب) - تحديث لإزالة النوع النصي - زكي الخولي
      const quizObj = lessons.flatMap(l => l.quizzes || []).find(q => q.id === quizId);
      let qType = questionForm[quizId]?.question_type || "mcq"; // تغيير الافتراضي من "text" إلى "mcq" - زكي الخولي
      if (qType === "mcq") qType = "multiple_choice";
      // points=1 تلقائيًا في الواجب
      let points = 1;
      if (quizObj && quizObj.quiz_type === 'exam') {
        points = Number(questionForm[quizId]?.points) || 1;
      }
      const data = {
        quiz: quizId,
        text: questionForm[quizId]?.text || "",
        question_type: qType,
        points: points,
        order: Number(questionForm[quizId]?.order) || 1,
      };
      console.log("[إرسال سؤال] البيانات:", data);

      // 1. إنشاء FormData لدعم رفع الصور - زكي الخولي
      const formData = new FormData();
      formData.append('quiz', quizId);
      formData.append('text', questionForm[quizId]?.text || "");
      formData.append('question_type', qType);
      formData.append('points', points);
      formData.append('order', Number(questionForm[quizId]?.order) || 1);

      // إضافة الصورة إذا كانت موجودة - زكي الخولي
      if (croppedImages[quizId]) {
        formData.append('image', croppedImages[quizId], 'question-image.jpg');
      }

      // 1. أضف السؤال أولاً مع الصورة - زكي الخولي
      const qRes = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/questions/`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      const questionId = qRes.data.id;
      // 2. إذا كان MCQ أو صح/خطأ أضف الإجابات
      if ((qType === 'multiple_choice' || qType === 'true_false') && answersForm[quizId]?.length > 0) {
        for (const ans of answersForm[quizId]) {
          await axios.post(
            `${process.env.NEXT_PUBLIC_API_URL}/api/answers/`,
            {
              question: questionId,
              text: ans.text,
              is_correct: ans.is_correct,
            },
            { headers: { Authorization: `Bearer ${token}` } }
          );
        }
      }
      setShowQuestionForm((prev) => ({ ...prev, [quizId]: false }));
      setQuestionForm((prev) => ({ ...prev, [quizId]: {} }));
      setAnswersForm((prev) => ({ ...prev, [quizId]: [] }));
      // تنظيف بيانات الصور بعد إضافة السؤال - زكي الخولي
      removeQuestionImage(quizId);
      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
    } catch (err) {
      setError("فشل في إضافة السؤال أو الإجابات");
      console.error("[خطأ إضافة سؤال]", err?.response?.data || err);
    } finally {
      setQuestionLoading((prev) => ({ ...prev, [quizId]: false }));
    }
  };

  // حذف سؤال
  const handleDeleteQuestion = async (quizId, questionId) => {
    if (!window.confirm("هل أنت متأكد من حذف هذا السؤال؟")) return;
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setQuestionLoading((prev) => ({ ...prev, [quizId]: true }));
    try {
      await axios.delete(`${process.env.NEXT_PUBLIC_API_URL}/api/questions/${questionId}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
      setQuestionEditForm(null);
    } catch {
      setError("فشل في حذف السؤال");
    } finally {
      setQuestionLoading((prev) => ({ ...prev, [quizId]: false }));
    }
  };

  // تعديل سؤال مع تعديل الإجابات
  const handleEditQuestion = async (quizId, questionId) => {
    const token = Cookies.get("authToken");
    if (!token) {
      setError("يرجى تسجيل الدخول أولاً");
      return;
    }
    setQuestionLoading((prev) => ({ ...prev, [quizId]: true }));
    try {
      // تجهيز نوع السؤال
      let qType = questionEditForm.question.question_type;
      if (qType === "mcq") qType = "multiple_choice";
      // 1. عدل السؤال نفسه
      await axios.patch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/questions/${questionId}/`,
        {
          text: questionEditForm.question.text,
          question_type: qType,
          points: Number(questionEditForm.question.points) || 1,
          order: Number(questionEditForm.question.order) || 1,
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      // 2. لو السؤال MCQ أو صح/خطأ: عدل الإجابات
      if ((qType === 'multiple_choice' || qType === 'true_false') && questionEditForm.answers && questionEditForm.answers.length > 0) {
        // احذف الإجابات القديمة غير الموجودة في answers الجديدة
        const oldAnswers = questionEditForm.question.answers || [];
        const newAnswers = questionEditForm.answers;
        const oldIds = oldAnswers.map(a => a.id);
        const newIds = newAnswers.filter(a => a.id).map(a => a.id);
        const toDelete = oldIds.filter(id => !newIds.includes(id));
        for (const id of toDelete) {
          await axios.delete(`${process.env.NEXT_PUBLIC_API_URL}/api/answers/${id}/`, { headers: { Authorization: `Bearer ${token}` } });
        }
        // عدل أو أضف الإجابات الجديدة
        for (const ans of newAnswers) {
          if (ans.id) {
            // تعديل إجابة موجودة
            await axios.patch(`${process.env.NEXT_PUBLIC_API_URL}/api/answers/${ans.id}/`, {
              text: ans.text,
              is_correct: ans.is_correct,
              question: questionId,
            }, { headers: { Authorization: `Bearer ${token}` } });
          } else {
            // إضافة إجابة جديدة
            await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/answers/`, {
              text: ans.text,
              is_correct: ans.is_correct,
              question: questionId,
            }, { headers: { Authorization: `Bearer ${token}` } });
          }
        }
      }
      setQuestionEditForm(null);
      // تحديث الدروس
      const lessonsList = await fetchInstructorLessons(courseId, token);
      setLessons(lessonsList);
    } catch (err) {
      setError("فشل في تعديل السؤال أو الإجابات");
      console.error("[خطأ تعديل سؤال]", err?.response?.data || err);
    } finally {
      setQuestionLoading((prev) => ({ ...prev, [quizId]: false }));
    }
  };

  // ================== مكون Image Cropper بسيط - زكي الخولي ==================
  const ImageCropperModal = ({ quizId, imageUrl, onCrop, onClose }) => {
    const canvasRef = useRef(null);
    const [cropArea, setCropArea] = useState({ x: 0, y: 0, width: 200, height: 200 });

    const handleCrop = () => {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = cropArea.width;
        canvas.height = cropArea.height;

        ctx.drawImage(
          img,
          cropArea.x, cropArea.y, cropArea.width, cropArea.height,
          0, 0, cropArea.width, cropArea.height
        );

        canvas.toBlob((blob) => {
          onCrop(blob);
        }, 'image/jpeg', 0.8);
      };

      img.src = imageUrl;
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <h3 className="text-lg font-bold mb-4">تعديل الصورة</h3>
          <div className="mb-4">
            <img src={imageUrl} alt="للتعديل" className="max-w-full h-48 object-contain border" />
          </div>
          <div className="space-y-2 mb-4">
            <label className="block text-sm">عرض القص:</label>
            <input
              type="range"
              min="50"
              max="400"
              value={cropArea.width}
              onChange={e => setCropArea(prev => ({ ...prev, width: parseInt(e.target.value), height: parseInt(e.target.value) }))}
              className="w-full"
            />
          </div>
          <canvas ref={canvasRef} style={{ display: 'none' }} />
          <div className="flex gap-2">
            <button
              onClick={handleCrop}
              className="bg-blue-600 text-white px-4 py-2 rounded"
            >
              تطبيق
            </button>
            <button
              onClick={onClose}
              className="bg-gray-300 px-4 py-2 rounded"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    );
  };

  // ================== واجهة المستخدم الرئيسية ==================
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }
  if (!courseData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-600">لم يتم العثور على بيانات الكورس</p>
        </div>
      </div>
    );
  }

  console.log("lessons:", lessons);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        {/* رأس الصفحة مع زر إضافة درس */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">تفاصيل الكورس</h1>
          <div className="flex gap-2">
            <Link
              href={`/instructor/dashboard/${courseId}/add-lesson`}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              إضافة درس جديد
            </Link>
            <button
              onClick={togglePublish}
              disabled={publishing}
              className={`px-6 py-2 rounded-lg font-semibold transition-colors ${courseData.is_published
                  ? "bg-red-600 hover:bg-red-700 text-white"
                  : "bg-green-600 hover:bg-green-700 text-white"
                }`}
            >
              {publishing
                ? "..."
                : courseData.is_published
                  ? "إلغاء النشر"
                  : "نشر"}
            </button>
          </div>
        </div>
        {/* معلومات الكورس */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* القسم الأيمن - معلومات الكورس */}
          <div className="space-y-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                معلومات الكورس
              </h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-600">
                  <span className="font-medium">عنوان الكورس:</span>{" "}
                  {courseData.title}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">الوصف:</span>{" "}
                  {courseData.description}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">الفئة:</span>{" "}
                  {courseData.category?.name}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">المستوى:</span>{" "}
                  {courseData.level}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">اللغة:</span>{" "}
                  {courseData.language}
                </p>
              </div>
            </div>
            {/* التقييمات والإحصائيات */}
            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                التقييمات والإحصائيات
              </h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-600">
                  <span className="font-medium">التقييم:</span>{" "}
                  {courseData.rating}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">عدد الطلاب:</span>{" "}
                  {courseData.students_count}
                </p>
              </div>
            </div>
          </div>
          {/* القسم الأيسر - الأسعار والوسائط */}
          <div className="space-y-4">
            {/* الأسعار */}
            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                الأسعار
              </h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-600">
                  <span className="font-medium">السعر:</span> {courseData.price}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">سعر الخصم:</span>{" "}
                  {courseData.discount_price}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">العملة:</span>{" "}
                  {courseData.currency}
                </p>
              </div>
            </div>
            {/* الوسائط */}
            <div>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                الوسائط
              </h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                {/* الصورة المصغرة */}
                <div className="mb-4">
                  <p className="font-medium text-gray-700 mb-2">
                    الصورة المصغرة:
                  </p>
                  <img
                    src={`https://res.cloudinary.com/di5y7hnub/${courseData.thumbnail}`}
                    alt="Course thumbnail"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                </div>
                {/* فيديو العرض */}
                <div>
                  <p className="font-medium text-gray-700 mb-2">فيديو العرض:</p>
                  <video controls className="w-full rounded-lg">
                    <source
                      src={`https://res.cloudinary.com/di5y7hnub/${courseData.promo_video}`}
                      type="video/mp4"
                    />
                    Your browser does not support the video tag.
                  </video>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* قسم الدروس */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">الدروس</h2>
          {lessons.length > 0 ? (
            <div className="space-y-4">
              {lessons.map((lesson) => (
                <div
                  key={lesson.id}
                  className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold">
                        {lesson.order}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800">
                          {lesson.title}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {lesson.lesson_type === "video"
                            ? "درس فيديو"
                            : "درس نصي"}
                        </p>
                        {lesson.resources && (
                          <div className="mt-1">
                            <a
                              href={lesson.resources}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 underline text-sm"
                            >
                              عرض الملف المرفق 📄
                            </a>
                            {deletingResource[lesson.id] ? (
                              <span className="ml-2 inline-block align-middle">
                                <svg className="animate-spin h-5 w-5 text-red-600" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
                                </svg>
                                <span className="text-xs text-gray-500 ml-1">جاري الحذف...</span>
                              </span>
                            ) : (
                              <button
                                onClick={() => handleDeleteResource(lesson.id)}
                                className="text-red-600 hover:underline ml-2"
                                disabled={deletingResource[lesson.id]}
                              >
                                حذف
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleSeeLesson(lesson.id)}
                        className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg"
                      >
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                          />
                        </svg>
                      </button>
                      <button
                        onClick={() => setExpandedLesson(expandedLesson === lesson.id ? null : lesson.id)}
                        className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg"
                      >
                        <svg className={`w-5 h-5 transform transition-transform ${expandedLesson === lesson.id ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleDeleteLesson(lesson.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  {/* شريط الأدوات المنسدلة */}
                  {expandedLesson === lesson.id && (
                    <div className="mt-4 bg-gray-50 rounded-lg p-4 border">
                      <div className="flex gap-2 mb-4">
                        <button
                          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
                          onClick={() => setShowQuizForm((prev) => ({ ...prev, [lesson.id]: 'exam' }))}
                        >
                          إضافة امتحان
                        </button>
                        <button
                          className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
                          onClick={() => setShowQuizForm((prev) => ({ ...prev, [lesson.id]: 'assignment' }))}
                        >
                          إضافة واجب
                        </button>
                        {!lesson.resources && (
                          <button
                            className="bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600"
                            onClick={() => setShowFileForm((prev) => ({ ...prev, [lesson.id]: true }))}
                          >
                            رفع ملف
                          </button>
                        )}
                      </div>
                      {/* فورم إضافة اختبار أو واجب */}
                      {showQuizForm[lesson.id] && (
                        <form
                          className="mb-4 bg-white p-3 rounded shadow flex flex-col gap-2"
                          onSubmit={e => {
                            e.preventDefault();
                            // إذا كان واجب، type assignment وtime_limit=0 وpassing_score=1
                            if (showQuizForm[lesson.id] === 'assignment') {
                              setQuizForm(prev => ({
                                ...prev,
                                [lesson.id]: {
                                  ...prev[lesson.id],
                                  quiz_type: 'assignment',
                                  time_limit: 0,
                                  passing_score: 1,
                                }
                              }));
                              handleAddQuiz(lesson.id, 'assignment');
                            } else {
                              handleAddQuiz(lesson.id, showQuizForm[lesson.id]);
                            }
                          }}
                        >
                          <input
                            type="text"
                            className="border rounded px-2 py-1"
                            placeholder={`عنوان ${showQuizForm[lesson.id] === 'exam' ? 'الامتحان' : 'الواجب'}`}
                            value={quizForm[lesson.id]?.title || ''}
                            onChange={e => setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], title: e.target.value } }))}
                            required
                          />
                          <textarea
                            className="border rounded px-2 py-1"
                            placeholder={`وصف ${showQuizForm[lesson.id] === 'exam' ? 'الامتحان' : 'الواجب'}`}
                            value={quizForm[lesson.id]?.description || ''}
                            onChange={e => setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], description: e.target.value } }))}
                          />
                          {/* لا يظهر input درجة النجاح في حالة الواجب */}
                          {showQuizForm[lesson.id] === 'exam' && (
                            <input
                              type="number"
                              className="border rounded px-2 py-1"
                              placeholder="درجة النجاح (افتراضي 70)"
                              value={quizForm[lesson.id]?.passing_score || ''}
                              onChange={e => setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], passing_score: e.target.value } }))}
                            />
                          )}
                          {/* لا يظهر input الوقت في حالة الواجب */}
                          {showQuizForm[lesson.id] === 'exam' && (
                            <input
                              type="number"
                              className="border rounded px-2 py-1"
                              placeholder="الوقت بالدقائق (0 = بدون حد)"
                              value={quizForm[lesson.id]?.time_limit || ''}
                              onChange={e => setQuizForm(prev => ({ ...prev, [lesson.id]: { ...prev[lesson.id], time_limit: e.target.value } }))}
                            />
                          )}
                          <div className="flex gap-2">
                            <button type="submit" className="bg-blue-600 text-white px-4 py-1 rounded">حفظ</button>
                            <button type="button" className="bg-gray-300 px-4 py-1 rounded" onClick={() => setShowQuizForm(prev => ({ ...prev, [lesson.id]: false }))}>إلغاء</button>
                          </div>
                        </form>
                      )}
                      {/* فورم رفع ملف */}
                      {showFileForm[lesson.id] && (
                        <form
                          className="mb-4 bg-white p-3 rounded shadow flex flex-col gap-2"
                          onSubmit={e => {
                            e.preventDefault();
                            handleAddFile(lesson.id);
                          }}
                        >
                          {/* ✅ لو مفيش ملف */}
                          {!uploadedFile[lesson.id] && (
                            <>
                              <input
                                type="file"
                                className="border rounded px-2 py-1 cursor-pointer"
                                ref={el => fileInputRefs.current[lesson.id] = el}
                                accept="application/pdf"
                                required
                              />

                              {/* ✅ شريط التحميل */}
                              {isUploading[lesson.id] && (
                                <div className="w-full bg-gray-200 rounded h-3 mt-2 overflow-hidden">
                                  <div
                                    className="bg-blue-500 h-full transition-all"
                                    style={{ width: `${uploadProgress[lesson.id] || 0}%` }}
                                  ></div>
                                </div>
                              )}

                              <div className="flex gap-2">
                                <button
                                  type="submit"
                                  className="bg-blue-600 text-white px-4 py-1 rounded disabled:opacity-50"
                                  disabled={isUploading[lesson.id]}
                                >
                                  {isUploading[lesson.id] ? "جارٍ الرفع..." : "رفع"}
                                </button>
                                <button
                                  type="button"
                                  className="bg-gray-300 px-4 py-1 rounded"
                                  onClick={() => setShowFileForm(prev => ({ ...prev, [lesson.id]: false }))}
                                  disabled={isUploading[lesson.id]}
                                >
                                  إلغاء
                                </button>
                              </div>
                            </>
                          )}

                         

                        </form>
                      )}
                      {/* عرض قائمة الامتحانات والواجبات */}
                      <div className="mt-4">
                        <h4 className="font-bold text-gray-700 mb-2">الامتحانات والواجبات</h4>
                        {lesson.quizzes && lesson.quizzes.length > 0 ? (
                          <ul className="space-y-2">
                            {lesson.quizzes.map((quiz) => (
                              <li key={quiz.id} className="bg-white border rounded p-3 flex flex-col md:flex-row md:items-center md:justify-between">
                                <div className="w-full">
                                  <span className="font-semibold text-blue-700">{quiz.quiz_type === 'exam' ? 'امتحان' : 'واجب'}:</span> {quiz.title}
                                  <div className="text-sm text-gray-600">{quiz.description}</div>
                                  <div className="text-xs text-gray-500">درجة النجاح: {quiz.passing_score} | الوقت: {quiz.time_limit} دقيقة</div>
                                  {/* عرض الأسئلة */}
                                  <div className="mt-2 border-t pt-2">
                                    <div className="flex items-center justify-between mb-1">
                                      <span className="font-bold text-gray-700">الأسئلة</span>
                                      <button
                                        className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600"
                                        onClick={() => setShowQuestionForm((prev) => ({ ...prev, [quiz.id]: !prev[quiz.id] }))}
                                      >
                                        {showQuestionForm[quiz.id] ? 'إغلاق' : 'إضافة سؤال'}
                                      </button>
                                    </div>
                                    {/* فورم إضافة سؤال - يجب أن يكون هنا فقط */}
                                    {showQuestionForm[quiz.id] && (
                                      <form
                                        className="mb-2 bg-white p-2 rounded shadow flex flex-col gap-2 border"
                                        onSubmit={async e => {
                                          e.preventDefault();
                                          await handleAddQuestionWithAnswers(quiz.id);
                                        }}
                                      >
                                        <input
                                          type="text"
                                          className="border rounded px-2 py-1"
                                          placeholder="نص السؤال"
                                          value={questionForm[quiz.id]?.text || ''}
                                          onChange={e => setQuestionForm(prev => ({ ...prev, [quiz.id]: { ...prev[quiz.id], text: e.target.value } }))}
                                          required
                                        />
                                        {/* تحديث dropdown لإزالة الأسئلة النصية - زكي الخولي */}
                                        <select
                                          className="border rounded px-2 py-1"
                                          value={questionForm[quiz.id]?.question_type || 'mcq'}
                                          onChange={e => {
                                            setQuestionForm(prev => ({ ...prev, [quiz.id]: { ...prev[quiz.id], question_type: e.target.value } }));
                                            // إعادة تعيين الإجابات عند تغيير النوع - زكي الخولي
                                            if (e.target.value === 'mcq' || e.target.value === 'true_false') {
                                              setAnswersForm(prev => ({ ...prev, [quiz.id]: e.target.value === 'true_false' ? [
                                                { text: 'صح', is_correct: false },
                                                { text: 'خطأ', is_correct: false },
                                              ] : [
                                                { text: '', is_correct: false },
                                                { text: '', is_correct: false },
                                              ] }));
                                            } else {
                                              setAnswersForm(prev => ({ ...prev, [quiz.id]: [] }));
                                            }
                                          }}
                                        >
                                          {/* إزالة خيار "نصية" - المعلم يريد فقط اختيار من متعدد وصح/خطأ - زكي الخولي */}
                                          <option value="mcq">اختيار من متعدد</option>
                                          <option value="true_false">صح أو خطأ</option>
                                        </select>
                                        {/* لا يظهر input الدرجة في الواجب */}
                                        {quiz.quiz_type === 'exam' && (
                                          <input
                                            type="number"
                                            className="border rounded px-2 py-1"
                                            placeholder="الدرجة"
                                            value={questionForm[quiz.id]?.points || ''}
                                            onChange={e => setQuestionForm(prev => ({ ...prev, [quiz.id]: { ...prev[quiz.id], points: e.target.value } }))}
                                          />
                                        )}
                                        <input
                                          type="number"
                                          className="border rounded px-2 py-1"
                                          placeholder="الترتيب"
                                          value={questionForm[quiz.id]?.order || ''}
                                          onChange={e => setQuestionForm(prev => ({ ...prev, [quiz.id]: { ...prev[quiz.id], order: e.target.value } }))}
                                        />
                                        {/* إضافة واجهة رفع الصور - زكي الخولي */}
                                        <div className="border rounded p-2">
                                          <label className="block text-sm font-bold mb-2">صورة السؤال (اختيارية)</label>
                                          {!imagePreview[quiz.id] ? (
                                            <input
                                              type="file"
                                              accept="image/*"
                                              className="border rounded px-2 py-1 w-full"
                                              onChange={e => {
                                                const file = e.target.files[0];
                                                if (file) handleImageSelect(quiz.id, file);
                                              }}
                                            />
                                          ) : (
                                            <div className="space-y-2">
                                              <img
                                                src={imagePreview[quiz.id]}
                                                alt="معاينة الصورة"
                                                className="max-w-full h-32 object-contain border rounded"
                                              />
                                              <div className="flex gap-2">
                                                <button
                                                  type="button"
                                                  className="bg-red-500 text-white px-2 py-1 rounded text-xs"
                                                  onClick={() => removeQuestionImage(quiz.id)}
                                                >
                                                  حذف الصورة
                                                </button>
                                                <button
                                                  type="button"
                                                  className="bg-blue-500 text-white px-2 py-1 rounded text-xs"
                                                  onClick={() => setShowImageCropper(prev => ({ ...prev, [quiz.id]: true }))}
                                                >
                                                  تعديل الصورة
                                                </button>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                        {/* إدخال الإجابات لو السؤال MCQ أو صح/خطأ */}
                                        {(questionForm[quiz.id]?.question_type === 'mcq' || questionForm[quiz.id]?.question_type === 'true_false') && (
                                          <div className="bg-gray-50 p-2 rounded border mt-2">
                                            <div className="font-bold text-sm mb-2">الإجابات</div>
                                            {answersForm[quiz.id]?.map((ans, idx) => (
                                              <div key={idx} className="flex items-center gap-2 mb-1">
                                                <input
                                                  type="text"
                                                  className="border rounded px-2 py-1 flex-1"
                                                  placeholder={`الإجابة ${idx + 1}`}
                                                  value={ans.text}
                                                  disabled={questionForm[quiz.id]?.question_type === 'true_false'}
                                                  onChange={e => setAnswersForm(prev => ({
                                                    ...prev,
                                                    [quiz.id]: prev[quiz.id].map((a, i) => i === idx ? { ...a, text: e.target.value } : a)
                                                  }))}
                                                  required
                                                />
                                                <input
                                                  type="radio"
                                                  name={`correct_${quiz.id}`}
                                                  checked={ans.is_correct}
                                                  onChange={() => setAnswersForm(prev => ({
                                                    ...prev,
                                                    [quiz.id]: prev[quiz.id].map((a, i) => ({ ...a, is_correct: i === idx }))
                                                  }))}
                                                />
                                                <span className="text-xs">صحيحة</span>
                                                {/* زر حذف الإجابة (فقط في MCQ) */}
                                                {questionForm[quiz.id]?.question_type === 'mcq' && answersForm[quiz.id].length > 2 && (
                                                  <button
                                                    type="button"
                                                    className="text-red-500 text-xs ml-2"
                                                    onClick={() => setAnswersForm(prev => ({
                                                      ...prev,
                                                      [quiz.id]: prev[quiz.id].filter((_, i) => i !== idx)
                                                    }))}
                                                  >
                                                    حذف
                                                  </button>
                                                )}
                                              </div>
                                            ))}
                                            {/* زر إضافة إجابة جديدة (فقط في MCQ) */}
                                            {questionForm[quiz.id]?.question_type === 'mcq' && (
                                              <button
                                                type="button"
                                                className="bg-gray-200 px-2 py-1 rounded text-xs mt-2"
                                                onClick={() => setAnswersForm(prev => ({
                                                  ...prev,
                                                  [quiz.id]: [...(prev[quiz.id] || []), { text: '', is_correct: false }]
                                                }))}
                                                disabled={answersForm[quiz.id]?.length >= 6}
                                              >
                                                + إضافة اختيار
                                              </button>
                                            )}
                                          </div>
                                        )}
                                        <div className="flex gap-2">
                                          <button type="submit" className="bg-blue-600 text-white px-4 py-1 rounded" disabled={questionLoading[quiz.id]}>حفظ</button>
                                          <button type="button" className="bg-gray-300 px-4 py-1 rounded" onClick={() => setShowQuestionForm(prev => ({ ...prev, [quiz.id]: false }))}>إلغاء</button>
                                        </div>
                                      </form>
                                    )}
                                    {/* قائمة الأسئلة */}
                                    {quiz.questions && quiz.questions.length > 0 ? (
                                      <ul className="space-y-1">
                                        {quiz.questions.map((question) => (
                                          <li key={question.id} className="bg-gray-50 border rounded p-2 flex flex-col md:flex-row md:items-center md:justify-between">
                                            <div>
                                              <span className="font-semibold text-indigo-700">س:</span> {question.text}
                                              {/* تحديث عرض نوع السؤال لإزالة النصية - زكي الخولي */}
                                              <span className="text-xs text-gray-500 ml-2">({question.question_type === 'mcq' || question.question_type === 'multiple_choice' ? 'اختيار من متعدد' : question.question_type === 'true_false' ? 'صح أو خطأ' : 'غير محدد'})</span>
                                              <span className="text-xs text-gray-400 ml-2">درجة: {question.points}</span>
                                              {/* عرض الإجابات لو السؤال MCQ أو صح/خطأ */}
                                              {(question.question_type === 'mcq' || question.question_type === 'multiple_choice' || question.question_type === 'true_false') && question.answers && question.answers.length > 0 && (
                                                <ul className="mt-2 ml-4 list-disc text-sm">
                                                  {question.answers.map((ans) => (
                                                    <li key={ans.id} className={ans.is_correct ? 'text-green-700 font-bold' : ''}>
                                                      {ans.text} {ans.is_correct && <span className="ml-1 text-green-600">✔</span>}
                                                    </li>
                                                  ))}
                                                </ul>
                                              )}
                                            </div>
                                            <div className="flex gap-2 mt-2 md:mt-0">
                                              <button
                                                className="bg-yellow-400 text-white px-2 py-1 rounded text-xs hover:bg-yellow-500"
                                                onClick={() => {
                                                  setQuestionEditForm({
                                                    quizId: quiz.id,
                                                    question: {
                                                      ...question,
                                                      question_type: question.question_type === 'multiple_choice' ? 'mcq' : question.question_type,
                                                    },
                                                    answers: (question.question_type === 'mcq' || question.question_type === 'multiple_choice' || question.question_type === 'true_false') && question.answers ? question.answers.map(a => ({ ...a })) : [],
                                                  });
                                                }}
                                              >
                                                تعديل
                                              </button>
                                              <button
                                                className="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600"
                                                onClick={() => handleDeleteQuestion(quiz.id, question.id)}
                                                disabled={questionLoading[quiz.id]}
                                              >
                                                حذف
                                              </button>
                                            </div>
                                          </li>
                                        ))}
                                      </ul>
                                    ) : (
                                      <div className="text-gray-400 text-xs">لا يوجد أسئلة بعد</div>
                                    )}
                                    {/* فورم تعديل سؤال */}
                                    {questionEditForm && questionEditForm.quizId === quiz.id && (
                                      <form
                                        className="mt-2 bg-white p-2 rounded shadow flex flex-col gap-2 border"
                                        onSubmit={e => {
                                          e.preventDefault();
                                          handleEditQuestion(quiz.id, questionEditForm.question.id);
                                        }}
                                      >
                                        <input
                                          type="text"
                                          className="border rounded px-2 py-1"
                                          placeholder="نص السؤال"
                                          value={questionEditForm.question.text || ''}
                                          onChange={e => setQuestionEditForm(prev => ({
                                            ...prev,
                                            question: { ...prev.question, text: e.target.value }
                                          }))}
                                          required
                                        />
                                        {/* تحديث dropdown التعديل لإزالة الأسئلة النصية - زكي الخولي */}
                                        <select
                                          className="border rounded px-2 py-1"
                                          value={questionEditForm.question.question_type || 'mcq'}
                                          onChange={e => {
                                            const newType = e.target.value;
                                            setQuestionEditForm(prev => ({
                                              ...prev,
                                              question: { ...prev.question, question_type: newType },
                                              answers: (newType === 'mcq' || newType === 'true_false')
                                                ? (newType === 'true_false'
                                                    ? [
                                                        { text: 'صح', is_correct: false },
                                                        { text: 'خطأ', is_correct: false },
                                                      ]
                                                    : [
                                                        { text: '', is_correct: false },
                                                        { text: '', is_correct: false },
                                                      ])
                                                : [],
                                            }));
                                          }}
                                        >
                                          {/* إزالة خيار "نصية" من التعديل أيضاً - زكي الخولي */}
                                          <option value="mcq">اختيار من متعدد</option>
                                          <option value="true_false">صح أو خطأ</option>
                                        </select>
                                        <input
                                          type="number"
                                          className="border rounded px-2 py-1"
                                          placeholder="الدرجة"
                                          value={questionEditForm.question.points || ''}
                                          onChange={e => setQuestionEditForm(prev => ({
                                            ...prev,
                                            question: { ...prev.question, points: e.target.value }
                                          }))}
                                        />
                                        <input
                                          type="number"
                                          className="border rounded px-2 py-1"
                                          placeholder="الترتيب"
                                          value={questionEditForm.question.order || ''}
                                          onChange={e => setQuestionEditForm(prev => ({
                                            ...prev,
                                            question: { ...prev.question, order: e.target.value }
                                          }))}
                                        />
                                        {/* إدخال الإجابات لو السؤال MCQ أو صح/خطأ */}
                                        {(questionEditForm.question.question_type === 'mcq' || questionEditForm.question.question_type === 'multiple_choice' || questionEditForm.question.question_type === 'true_false') && (
                                          <div className="bg-gray-50 p-2 rounded border mt-2">
                                            <div className="font-bold text-sm mb-2">الإجابات</div>
                                            {questionEditForm.answers?.map((ans, idx) => (
                                              <div key={ans.id || idx} className="flex items-center gap-2 mb-1">
                                                <input
                                                  type="text"
                                                  className="border rounded px-2 py-1 flex-1"
                                                  placeholder={`الإجابة ${idx + 1}`}
                                                  value={ans.text}
                                                  disabled={questionEditForm.question.question_type === 'true_false'}
                                                  onChange={e => setQuestionEditForm(prev => ({
                                                    ...prev,
                                                    answers: prev.answers.map((a, i) => i === idx ? { ...a, text: e.target.value } : a)
                                                  }))}
                                                  required
                                                />
                                                <input
                                                  type="radio"
                                                  name={`edit_correct_${quiz.id}`}
                                                  checked={ans.is_correct}
                                                  onChange={() => setQuestionEditForm(prev => ({
                                                    ...prev,
                                                    answers: prev.answers.map((a, i) => ({ ...a, is_correct: i === idx }))
                                                  }))}
                                                />
                                                <span className="text-xs">صحيحة</span>
                                                {/* زر حذف الإجابة (فقط في MCQ) */}
                                                {(questionEditForm.question.question_type === 'mcq' || questionEditForm.question.question_type === 'multiple_choice') && questionEditForm.answers.length > 2 && (
                                                  <button
                                                    type="button"
                                                    className="text-red-500 text-xs ml-2"
                                                    onClick={() => setQuestionEditForm(prev => ({
                                                      ...prev,
                                                      answers: prev.answers.filter((_, i) => i !== idx)
                                                    }))}
                                                  >
                                                    حذف
                                                  </button>
                                                )}
                                              </div>
                                            ))}
                                            {/* زر إضافة إجابة جديدة (فقط في MCQ) */}
                                            {(questionEditForm.question.question_type === 'mcq' || questionEditForm.question.question_type === 'multiple_choice') && (
                                              <button
                                                type="button"
                                                className="bg-gray-200 px-2 py-1 rounded text-xs mt-2"
                                                onClick={() => setQuestionEditForm(prev => ({
                                                  ...prev,
                                                  answers: [...(prev.answers || []), { text: '', is_correct: false }]
                                                }))}
                                                disabled={questionEditForm.answers?.length >= 6}
                                              >
                                                + إضافة اختيار
                                              </button>
                                            )}
                                          </div>
                                        )}
                                        <div className="flex gap-2">
                                          <button type="submit" className="bg-blue-600 text-white px-4 py-1 rounded" disabled={questionLoading[quiz.id]}>حفظ</button>
                                          <button type="button" className="bg-gray-300 px-4 py-1 rounded" onClick={() => setQuestionEditForm(null)}>إلغاء</button>
                                        </div>
                                      </form>
                                    )}
                                  </div>
                                </div>
                                <div className="flex gap-2 mt-2 md:mt-0">
                                  <button
                                    className="bg-yellow-400 text-white px-3 py-1 rounded hover:bg-yellow-500"
                                    onClick={() => setQuizEditForm({ lessonId: lesson.id, quiz })}
                                  >
                                    تعديل
                                  </button>
                                  <button
                                    className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600"
                                    onClick={() => handleDeleteQuiz(lesson.id, quiz.id)}
                                  >
                                    حذف
                                  </button>
                                </div>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <div className="text-gray-400 text-sm">لا يوجد امتحانات أو واجبات لهذا الدرس</div>
                        )}
                      </div>
                      {/* فورم تعديل كويز */}
                      {quizEditForm && quizEditForm.lessonId === lesson.id && (
                        <form
                          className="mt-4 bg-white p-3 rounded shadow flex flex-col gap-2 border"
                          onSubmit={e => {
                            e.preventDefault();
                            handleEditQuiz(lesson.id, quizEditForm.quiz.id);
                          }}
                        >
                          <input
                            type="text"
                            className="border rounded px-2 py-1"
                            placeholder="عنوان الامتحان/الواجب"
                            value={quizEditForm.quiz.title}
                            onChange={e => setQuizEditForm(prev => ({ ...prev, quiz: { ...prev.quiz, title: e.target.value } }))}
                            required
                          />
                          <textarea
                            className="border rounded px-2 py-1"
                            placeholder="وصف الامتحان/الواجب"
                            value={quizEditForm.quiz.description}
                            onChange={e => setQuizEditForm(prev => ({ ...prev, quiz: { ...prev.quiz, description: e.target.value } }))}
                          />
                          <input
                            type="number"
                            className="border rounded px-2 py-1"
                            placeholder="درجة النجاح"
                            value={quizEditForm.quiz.passing_score}
                            onChange={e => setQuizEditForm(prev => ({ ...prev, quiz: { ...prev.quiz, passing_score: e.target.value } }))}
                          />
                          <input
                            type="number"
                            className="border rounded px-2 py-1"
                            placeholder="الوقت بالدقائق"
                            value={quizEditForm.quiz.time_limit}
                            onChange={e => setQuizEditForm(prev => ({ ...prev, quiz: { ...prev.quiz, time_limit: e.target.value } }))}
                          />
                          <div className="flex gap-2">
                            <button type="submit" className="bg-blue-600 text-white px-4 py-1 rounded">حفظ التعديلات</button>
                            <button type="button" className="bg-gray-300 px-4 py-1 rounded" onClick={() => setQuizEditForm(null)}>إلغاء</button>
                          </div>
                        </form>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">لا توجد دروس متاحة</p>
          )}
        </div>
        {/* قسم التقييمات */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            تقييمات الطلاب
          </h2>
          {courseData.reviews && courseData.reviews.length > 0 ? (
            <div className="space-y-4">
              {courseData.reviews.map((review) => (
                <div
                  key={review.id}
                  className="bg-gray-50 rounded-lg p-4 flex flex-col md:flex-row md:items-center md:justify-between"
                >
                  <div>
                    <p className="text-gray-700 font-semibold">
                      {review.user?.username || "طالب مجهول"}
                    </p>
                    <p className="text-yellow-500">
                      التقييم: {review.rating} ⭐
                    </p>
                    <p className="text-gray-600">{review.comment}</p>
                    <p className="text-xs text-gray-400">
                      {review.created_at &&
                        review.created_at.replace("T", " ").substring(0, 16)}
                    </p>
                    {review.reply && (
                      <div className="mt-2 p-2 bg-blue-50 rounded text-blue-800">
                        <span className="font-bold">
                          {courseData.instructor?.username || "المدرب"}:
                        </span>{" "}
                        {review.reply}
                      </div>
                    )}
                    {/* تعليقات التقييمات */}
                    <div className="mt-2">
                      {/* يمكن إضافة مكون شجرة التعليقات هنا */}
                      <div className="flex gap-2 mt-2">
                        <input
                          type="text"
                          className="border rounded px-2 py-1 flex-1"
                          placeholder="اكتب تعليقك..."
                          value={commentText[review.id] || ""}
                          onChange={(e) =>
                            setCommentText((prev) => ({
                              ...prev,
                              [review.id]: e.target.value,
                            }))
                          }
                        />
                        <button
                          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 disabled:opacity-50"
                          disabled={
                            commentLoading[review.id] || !commentText[review.id]
                          }
                          onClick={() => handleAddComment(review.id)}
                        >
                          إضافة
                        </button>
                      </div>
                    </div>
                  </div>
                  {!review.is_approved && (
                    <div className="mt-2 md:mt-0 flex gap-2">
                      <button
                        className="bg-green-500 text-white px-4 py-1 rounded hover:bg-green-600 disabled:opacity-50"
                        disabled={approving}
                        onClick={() => handleApproveReview(review.id, true)}
                      >
                        موافقة
                      </button>
                      <button
                        className="bg-red-500 text-white px-4 py-1 rounded hover:bg-red-600 disabled:opacity-50"
                        disabled={approving}
                        onClick={() => handleApproveReview(review.id, false)}
                      >
                        رفض
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">لا توجد تقييمات بعد</p>
          )}
        </div>
        {/* قسم المشتركين */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            المشتركين في الكورس ({courseData.students_count})
          </h2>
          {courseData.students && courseData.students.length > 0 ? (
            <ul className="list-disc pl-6">
              {courseData.students.map((student, idx) => (
                <li
                  key={student.id || student.email || student.username || idx}
                >
                  {student.username} ({student.email})
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">لا يوجد مشتركين بعد</p>
          )}
        </div>
      </div>
    </div>
  );
}
