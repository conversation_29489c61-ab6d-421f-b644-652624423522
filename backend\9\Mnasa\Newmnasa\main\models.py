from django.db import models
from cloudinary.models import Cloudinary<PERSON>ield  
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
import uuid
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
import secrets

class User(AbstractUser):
    # معلومات أساسية
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    profile_image = models.ImageField(upload_to='profile_images/', blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    first_name = models.CharField(max_length=30, blank=True, null=True)
    last_name = models.CharField(max_length=30, blank=True, null=True)
    # التحقق والأمان
    email_verified = models.BooleanField(default=False)
    verification_token = models.CharField(max_length=100, blank=True, null=True)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)
    
    # الأدوار
    is_instructor = models.BooleanField(default=False)
    is_student = models.BooleanField(default=True)
    
    # تفضيلات المستخدم
    language = models.CharField(max_length=10, default='ar', choices=[('ar', 'العربية'), ('en', 'English')])
    
    # معلومات الدفع
    wallet_number = models.CharField(max_length=50, blank=True, null=True, unique=True)
    payment_method = models.CharField(
        max_length=50,
        choices=[
            ('vodafone_cash', 'فودافون كاش'),
            ('orange_money', 'أورانج موني'),
            ('we_cash', 'وي كاش'),
            ('etisalat_cash', 'اتصالات كاش'),
        ],
        blank=True,
        null=True,
    )
    
    # علاقات الصلاحيات
    groups = models.ManyToManyField(
        'auth.Group',
        related_name='mnasa_users',
        blank=True,
        help_text='The groups this user belongs to.',
        verbose_name='groups',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='mnasa_users',
        blank=True,
        help_text='Specific permissions for this user.',
        verbose_name='user permissions',
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.verification_token:
            self.verification_token = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.username

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        permissions = [
            ('can_teach', 'Can create and teach courses'),
        ]

class InstructorProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='instructor_profile')
    specialization = models.CharField(max_length=100)
    qualifications = models.TextField()
    website = models.URLField(max_length=255, default="", blank=True)
    linkedin = models.URLField(max_length=255, default="", blank=True)
    is_approved = models.BooleanField(default=False)
    payment_details = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Instructor Profile for {self.user.username}"

class Category(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    description = models.TextField()
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, related_name='subcategories')
    
    def __str__(self):
        return self.name

class Course(models.Model):
    LEVEL_CHOICES = [
        ('beginner', 'مبتدئ'),
        ('intermediate', 'متوسط'),
        ('advanced', 'متقدم'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True, null=True, blank=True)
    description = models.TextField()
    short_description = models.CharField(max_length=300, blank=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, related_name='courses')
    instructor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='taught_courses')
    students = models.ManyToManyField(User, related_name='enrolled_courses', blank=True)
    likes = models.ManyToManyField(User, related_name='liked_courses', blank=True)
    
    price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    currency = models.CharField(max_length=3, default='USD')
    
    language = models.CharField(max_length=20, default='Arabic')
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default='beginner')
    prerequisites = models.TextField(blank=True)
    learning_outcomes = models.TextField(blank=True)
    
    max_students = models.PositiveIntegerField(null=True, blank=True)
    is_published = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    thumbnail = CloudinaryField('image')
    promo_video = CloudinaryField(resource_type="video", blank=True, null=True)
    
    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
            original_slug = self.slug
            counter = 1
            while Course.objects.filter(slug=self.slug).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

class Lesson(models.Model):
    LESSON_TYPES = [
        ('video', 'فيديو'),
        ('article', 'مقال'),
        ('quiz', 'اختبار'),
        ('assignment', 'واجب'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='lessons')
    title = models.CharField(max_length=200, default='درس جديد')
    order = models.PositiveIntegerField(default=1)
    lesson_type = models.CharField(max_length=20, choices=LESSON_TYPES, default='video')
    content = models.TextField(default='')
    
    is_preview = models.BooleanField(default=False)
    
    video = CloudinaryField(resource_type='video', folder='course_videos/', blank=True, null=True)
    duration = models.PositiveIntegerField(help_text="Duration in minutes", default=0)
    video_public_id = models.CharField(max_length=255, blank=True, null=True)
    video_duration = models.PositiveIntegerField(default=0)
    is_drm_protected = models.BooleanField(default=True, help_text="حماية الفيديو باستخدام DRM")
    is_hls_encrypted = models.BooleanField(default=True, help_text="تشفير HLS باستخدام AES-128")
    token_expiry_hours = models.PositiveIntegerField(default=24, help_text="مدة صلاحية توكن الوصول")
    watermark_enabled = models.BooleanField(default=True, help_text="تفعيل العلامة المائية")
    
    resources = CloudinaryField(
    resource_type='raw',
    folder='lesson_resources/',
    type='upload',        # ✅ يخلي الملف Public
    blank=True,
    null=True
    )

    
    class Meta:
        ordering = ['order']
        unique_together = ('course', 'order')
    
    def __str__(self):
        return f"{self.course.title} - {self.title}"

class Enrollment(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(User, on_delete=models.CASCADE, related_name='enrollments')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='enrollments')
    enrolled_at = models.DateTimeField(auto_now_add=True)
    completed = models.BooleanField(default=False)
    
    progress = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    last_accessed = models.DateTimeField(auto_now=True)
    current_lesson = models.ForeignKey(Lesson, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        unique_together = ('student', 'course')
    
    def __str__(self):
        return f"{self.student.username} in {self.course.title}"

class Review(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reviews')
    rating = models.PositiveIntegerField(choices=[(i, i) for i in range(1, 6)])
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_approved = models.BooleanField(default=False)
    reply = models.TextField(default="", blank=True)  # كان blank=True, null=True
    
    def __str__(self):
        return f"Review by {self.user.username} for {self.course.title}"

class ReviewComment(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    review = models.ForeignKey('Review', on_delete=models.CASCADE, related_name='comments')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='review_comments')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    parent = models.ForeignKey('self', default=None, null=True, blank=True, on_delete=models.CASCADE, related_name='replies')  # أبقى null=True لأن parent قد يكون None

    def __str__(self):
        return f"Comment by {self.user.username} on review {self.review.id}"

class DigitalProduct(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    seller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='products')
    
    file = models.FileField(upload_to='digital_products/')
    thumbnail = models.ImageField(upload_to='product_thumbnails/')
    
    download_limit = models.PositiveIntegerField(default=3)
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title

class Order(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders')
    course = models.ForeignKey(Course, on_delete=models.SET_NULL, null=True, blank=True, related_name='orders')
    product = models.ForeignKey(DigitalProduct, on_delete=models.SET_NULL, null=True, blank=True, related_name='orders')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    paymob_order_id = models.CharField(max_length=100, blank=True, null=True)
    
    billing_email = models.EmailField(null=True, blank=True)
    billing_name = models.CharField(max_length=100, null=True, blank=True)
    billing_address = models.TextField(blank=True, null=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Order {self.id} - {self.user.username}"

    def save(self, *args, **kwargs):
        if not self.billing_email and self.user.email:
            self.billing_email = self.user.email
        if not self.billing_name:
            self.billing_name = self.user.get_full_name() or self.user.username
        super().save(*args, **kwargs)

class Payment(models.Model):
    PAYMENT_METHODS = (
        ('credit_card', 'Credit Card'),
        ('paypal', 'PayPal'),
        ('bank_transfer', 'Bank Transfer'),
        ('vodafone_cash', 'Vodafone Cash'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50, choices=PAYMENT_METHODS)
    transaction_id = models.CharField(max_length=100)
    status = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    
    payment_details = models.JSONField(blank=True, null=True)
    
    def __str__(self):
        return f"Payment {self.id} - {self.order.id}"

class Certificate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='certificates')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='certificates')
    issued_at = models.DateTimeField(auto_now_add=True)
    certificate_url = models.URLField()
    verification_code = models.CharField(max_length=20, unique=True)
    
    def __str__(self):
        return f"Certificate for {self.user.username} - {self.course.title}"

class Quiz(models.Model):
    QUIZ_TYPE_CHOICES = [
        ('exam', 'امتحان'),
        ('assignment', 'واجب'),
    ]
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name='quizzes')
    title = models.CharField(max_length=200)
    description = models.TextField()
    passing_score = models.PositiveIntegerField(default=70)
    time_limit = models.IntegerField(default=0)
    quiz_type = models.CharField(max_length=20, choices=QUIZ_TYPE_CHOICES, default='exam')
    
    def __str__(self):
        return f"Quiz for {self.lesson.title} ({self.get_quiz_type_display()})"

# تحديث نموذج السؤال لإضافة دعم الصور وإزالة الأسئلة النصية - زكي الخولي
class Question(models.Model):
    QUESTION_TYPES = [
        ('multiple_choice', 'Multiple Choice'),
        ('true_false', 'True/False'),
        # تم إزالة 'short_answer' لأن المعلم يريد فقط اختيار من متعدد وصح/خطأ - زكي الخولي
    ]

    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='questions')
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES)
    text = models.TextField()
    # إضافة حقل الصورة للسؤال مع إمكانية أن يكون فارغ - زكي الخولي
    image = models.ImageField(upload_to='question_images/', blank=True, null=True, help_text='صورة اختيارية للسؤال')
    points = models.PositiveIntegerField(default=1)
    order = models.PositiveIntegerField()

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"Question: {self.text[:50]}..."

class Answer(models.Model):
    question = models.ForeignKey(Question, on_delete=models.CASCADE, related_name='answers')
    text = models.TextField()
    is_correct = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Answer: {self.text[:50]}..."

class UserQuizAttempt(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='quiz_attempts')
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='attempts')
    score = models.PositiveIntegerField(default=0)
    passed = models.BooleanField(default=False)
    answers = models.JSONField(default=dict, blank=True)  # تخزين إجابات الطالب
    submitted = models.BooleanField(default=False)  # هل تم تسليم الامتحان
    submitted_at = models.DateTimeField(null=True, blank=True)  # وقت التسليم
    created_at = models.DateTimeField(auto_now_add=True)  # وقت بدء المحاولة
    completed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Attempt by {self.user.username} on {self.quiz.title}"

class Announcement(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='announcements')
    title = models.CharField(max_length=200)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Announcement: {self.title} for {self.course.title}"

class FAQ(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='faqs')
    question = models.CharField(max_length=200)
    answer = models.TextField()
    order = models.PositiveIntegerField(default=1)

    def __str__(self):
        return f"FAQ: {self.question} for {self.course.title}"

class InstructorAvailability(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='availabilities')
    day = models.CharField(max_length=10, choices=[
        ('saturday', 'Saturday'),
        ('sunday', 'Sunday'),
        ('monday', 'Monday'),
        ('tuesday', 'Tuesday'),
        ('wednesday', 'Wednesday'),
        ('thursday', 'Thursday'),
        ('friday', 'Friday'),
    ])
    from_time = models.CharField(max_length=10)
    to_time = models.CharField(max_length=10)
    timezone = models.CharField(max_length=50, default='Africa/Cairo')
    enabled = models.BooleanField(default=True)
    note = models.CharField(max_length=255, default="بدون ملاحظات", blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.day}: {self.from_time} to {self.to_time} ({self.note})"

# إنشاء InstructorProfile تلقائياً عند إنشاء مستخدم معلم جديد
@receiver(post_save, sender=User)
def create_instructor_profile(sender, instance, created, **kwargs):
    if created and instance.is_instructor:
        from .models import InstructorProfile
        if not hasattr(instance, 'instructor_profile'):
            InstructorProfile.objects.create(user=instance, specialization='', qualifications='')

class Notification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    message = models.TextField()
    link = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    type = models.CharField(max_length=50, default='SYSTEM')

    def __str__(self):
        return f"{self.user.username} - {'Read' if self.is_read else 'Unread'}"