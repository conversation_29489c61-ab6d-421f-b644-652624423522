// خدمات الطالب: جميع استدعاءات API الخاصة بالطالب هنا
import axios from "axios";
import { API_BASE_URL } from '../config/api';

export async function fetchStudentCourse(courseId, token, signal) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/courses/${courseId}/`, {
    headers,
    withCredentials: true,
    signal,
  });
  return response.data;
}

export async function fetchCourseLessons(courseId, token, signal) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/courses/${courseId}/lessons/`, {
    headers,
    withCredentials: true,
    signal,
  });
  return response.data.results || response.data || [];
}

export async function fetchCourseReviews(courseId) {
  const response = await axios.get(`${API_BASE_URL}/api/reviews/?course=${courseId}`);
  return response.data;
}

export async function submitCourseReview(courseId, data, token) {
  await axios.post(`${API_BASE_URL}/api/reviews/`, {
    course: courseId,
    rating: data.rating,
    comment: data.comment
  }, {
    headers: { Authorization: `Bearer ${token}` }
  });
}

export async function fetchReviewComments(reviewId) {
  const res = await axios.get(`${API_BASE_URL}/api/reviews/${reviewId}/comments/`);
  return res.data;
}

export async function toggleCourseLike(courseId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(`${API_BASE_URL}/api/courses/${courseId}/toggle_like/`, {}, { headers });
  return res.data;
}

export async function toggleCommentLike(commentId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(`${API_BASE_URL}/api/comments/${commentId}/toggle_like/`, {}, { headers });
  return res.data;
}

export async function addReviewReply(reviewId, parentId, replyText, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  await axios.post(`${API_BASE_URL}/api/reviews/${reviewId}/add_comment/`, { text: replyText, parent: parentId }, { headers });
}

export async function fetchStudentProfile(studentId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/users/list-all/?id=${studentId}`,
    { headers });
  return response.data.users?.find(user => user.id === studentId);
}

export async function fetchStudentCourses(studentId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/courses/?students=${studentId}`,
    { headers });
  // تصفية الدورات لعرض دورات الطالب فقط
  return response.data.filter(course => course.students && course.students.some(s => s.id === studentId));
}

// جلب الوقت المتبقي للامتحان للطالب - zaki alkholy
export async function fetchExamTimeLeft(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.get(`${API_BASE_URL}/api/quizzes/${quizId}/get_time_left/`, { headers });
  return res.data;
}

// جلب حالة الامتحان ونتيجته للطالب
export async function fetchExamStatus(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.get(`${API_BASE_URL}/api/quizzes/${quizId}/exam_status/`, { headers });
  return res.data;
}

// حفظ إجابة سؤال في الامتحان
export async function saveExamAnswer(quizId, questionId, answer, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const body = {
    question_id: questionId,
    answer_id: answer, // يجب أن يكون answer_id وليس answer
  };
  // تعديل: استخدام PATCH بدلاً من POST
  const res = await axios.patch(`${API_BASE_URL}/api/quizzes/${quizId}/save_answer/`, body, { headers });
  return res.data;
}

// بدء الامتحان - zaki alkholy
export async function startExam(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(`${API_BASE_URL}/api/quizzes/${quizId}/start/`, {}, { headers });
  return res.data;
}

// فحص انتهاء الوقت والتسليم التلقائي - zaki alkholy
export async function checkTimeAndAutoSubmit(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(`${API_BASE_URL}/api/quizzes/${quizId}/check_time_and_auto_submit/`, {}, { headers });
  return res.data;
}

// تسليم الامتحان
export async function submitExam(quizId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const res = await axios.post(`${API_BASE_URL}/api/quizzes/${quizId}/submit/`, {}, { headers });
  return res.data;
}
